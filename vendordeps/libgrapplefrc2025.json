{"fileName": "libgrapplefrc2025.json", "name": "libgrapplefrc", "version": "2025.1.3", "frcYear": "2025", "uuid": "8ef3423d-**************-206dae1d7168", "mavenUrls": ["https://storage.googleapis.com/grapple-frc-maven"], "jsonUrl": "https://storage.googleapis.com/grapple-frc-maven/libgrapplefrc2025.json", "javaDependencies": [{"groupId": "au.grapplerobotics", "artifactId": "lib<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "2025.1.3"}], "jniDependencies": [{"groupId": "au.grapplerobotics", "artifactId": "libgrapplefrcdriver", "version": "2025.1.3", "skipInvalidPlatforms": true, "isJar": false, "validPlatforms": ["windowsx86-64", "windowsx86", "linuxarm64", "linuxx86-64", "linux<PERSON>ena", "linuxarm32", "osxuniversal"]}], "cppDependencies": [{"groupId": "au.grapplerobotics", "artifactId": "libgrapplefrccpp", "version": "2025.1.3", "libName": "grapplefrc", "headerClassifier": "headers", "sharedLibrary": true, "skipInvalidPlatforms": true, "binaryPlatforms": ["windowsx86-64", "windowsx86", "linuxarm64", "linuxx86-64", "linux<PERSON>ena", "linuxarm32", "osxuniversal"]}, {"groupId": "au.grapplerobotics", "artifactId": "libgrapplefrcdriver", "version": "2025.1.3", "libName": "grapplefrcdriver", "headerClassifier": "headers", "sharedLibrary": true, "skipInvalidPlatforms": true, "binaryPlatforms": ["windowsx86-64", "windowsx86", "linuxarm64", "linuxx86-64", "linux<PERSON>ena", "linuxarm32", "osxuniversal"]}]}