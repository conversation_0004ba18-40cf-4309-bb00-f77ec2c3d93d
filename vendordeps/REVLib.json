{"fileName": "REVLib.json", "name": "REVLib", "version": "2026.0.1", "frcYear": "2026", "uuid": "3f48eb8c-50fe-43a6-9cb7-44c86353c4cb", "mavenUrls": ["https://maven.revrobotics.com/"], "jsonUrl": "https://software-metadata.revrobotics.com/REVLib-2026.json", "javaDependencies": [{"groupId": "com.revrobotics.frc", "artifactId": "REVLib-java", "version": "2026.0.1"}], "jniDependencies": [{"groupId": "com.revrobotics.frc", "artifactId": "R<PERSON><PERSON><PERSON>ib-driver", "version": "2026.0.1", "skipInvalidPlatforms": true, "isJar": false, "validPlatforms": ["windowsx86-64", "linuxarm64", "linuxx86-64", "linux<PERSON>ena", "linuxarm32", "osxuniversal"]}, {"groupId": "com.revrobotics.frc", "artifactId": "RevLibBackendDriver", "version": "2026.0.1", "skipInvalidPlatforms": true, "isJar": false, "validPlatforms": ["windowsx86-64", "linuxarm64", "linuxx86-64", "linux<PERSON>ena", "linuxarm32", "osxuniversal"]}, {"groupId": "com.revrobotics.frc", "artifactId": "RevLibWpiBackendDriver", "version": "2026.0.1", "skipInvalidPlatforms": true, "isJar": false, "validPlatforms": ["windowsx86-64", "linuxarm64", "linuxx86-64", "linux<PERSON>ena", "linuxarm32", "osxuniversal"]}], "cppDependencies": [{"groupId": "com.revrobotics.frc", "artifactId": "REVLib-cpp", "version": "2026.0.1", "libName": "REVLib", "headerClassifier": "headers", "sharedLibrary": false, "skipInvalidPlatforms": true, "binaryPlatforms": ["windowsx86-64", "linuxarm64", "linuxx86-64", "linux<PERSON>ena", "linuxarm32", "osxuniversal"]}, {"groupId": "com.revrobotics.frc", "artifactId": "R<PERSON><PERSON><PERSON>ib-driver", "version": "2026.0.1", "libName": "REVLibDriver", "headerClassifier": "headers", "sharedLibrary": false, "skipInvalidPlatforms": true, "binaryPlatforms": ["windowsx86-64", "linuxarm64", "linuxx86-64", "linux<PERSON>ena", "linuxarm32", "osxuniversal"]}, {"groupId": "com.revrobotics.frc", "artifactId": "RevLibBackendDriver", "version": "2026.0.1", "libName": "BackendDriver", "sharedLibrary": true, "skipInvalidPlatforms": true, "binaryPlatforms": ["windowsx86-64", "linuxarm64", "linuxx86-64", "linux<PERSON>ena", "linuxarm32", "osxuniversal"]}, {"groupId": "com.revrobotics.frc", "artifactId": "RevLibWpiBackendDriver", "version": "2026.0.1", "libName": "REVLibWpi", "sharedLibrary": true, "skipInvalidPlatforms": true, "binaryPlatforms": ["windowsx86-64", "linuxarm64", "linuxx86-64", "linux<PERSON>ena", "linuxarm32", "osxuniversal"]}]}