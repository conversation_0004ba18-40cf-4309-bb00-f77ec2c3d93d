{"fileName": "AdvantageKit.json", "name": "AdvantageKit", "version": "26.0.0", "uuid": "d820cc26-74e3-11ec-90d6-0242ac120003", "frcYear": "2026", "mavenUrls": ["https://frcmaven.wpi.edu/artifactory/littletonrobotics-mvn-release/"], "jsonUrl": "https://github.com/Mechanical-Advantage/AdvantageKit/releases/latest/download/AdvantageKit.json", "javaDependencies": [{"groupId": "org.littletonrobotics.akit", "artifactId": "akit-java", "version": "26.0.0"}], "jniDependencies": [{"groupId": "org.littletonrobotics.akit", "artifactId": "akit-wpilibio", "version": "26.0.0", "skipInvalidPlatforms": false, "isJar": false, "validPlatforms": ["linux<PERSON>ena", "linuxx86-64", "linuxarm64", "osxuniversal", "windowsx86-64"]}], "cppDependencies": []}