{"fileName": "ChoreoLib-2025.0.3.json", "name": "ChoreoLib", "version": "2025.0.3", "uuid": "b5e23f0a-dac9-4ad2-8dd6-02767c520aca", "frcYear": "2025", "mavenUrls": ["https://lib.choreo.autos/dep", "https://repo1.maven.org/maven2"], "jsonUrl": "https://lib.choreo.autos/dep/ChoreoLib2025.json", "javaDependencies": [{"groupId": "choreo", "artifactId": "ChoreoLib-java", "version": "2025.0.3"}, {"groupId": "com.google.code.gson", "artifactId": "gson", "version": "2.11.0"}], "jniDependencies": [], "cppDependencies": [{"groupId": "choreo", "artifactId": "ChoreoLib-cpp", "version": "2025.0.3", "libName": "ChoreoLib", "headerClassifier": "headers", "sharedLibrary": false, "skipInvalidPlatforms": true, "binaryPlatforms": ["windowsx86-64", "linuxx86-64", "osxuniversal", "linux<PERSON>ena", "linuxarm32", "linuxarm64"]}]}